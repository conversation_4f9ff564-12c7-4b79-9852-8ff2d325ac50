name: (Compiler) Publish Prereleases Manual

on:
  workflow_dispatch:
    inputs:
      prerelease_commit_sha:
        required: false
      release_channel:
        required: true
        type: string
      dist_tag:
        required: true
        type: string
      version_name:
        required: true
        type: string
      tag_version:
        required: false
        type: string

permissions: {}

env:
  TZ: /usr/share/zoneinfo/America/Los_Angeles

jobs:
  publish_prerelease_experimental:
    name: Publish to Experimental channel
    uses: facebook/react/.github/workflows/compiler_prereleases.yml@main
    with:
      commit_sha: ${{ inputs.prerelease_commit_sha || github.sha }}
      release_channel: ${{ inputs.release_channel }}
      dist_tag: ${{ inputs.dist_tag }}
      version_name: ${{ inputs.version_name }}
      tag_version: ${{ inputs.tag_version }}
    secrets:
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
