name: (Runtime) Publish Prereleases Nightly

on:
  schedule:
    # At 10 minutes past 16:00 on <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>
    <PERSON> <PERSON><PERSON>: 10 16 * * 1,2,3,4,5

permissions: {}

env:
  TZ: /usr/share/zoneinfo/America/Los_Angeles

jobs:
  publish_prerelease_canary:
    name: Publish to Canary channel
    uses: facebook/react/.github/workflows/runtime_prereleases.yml@main
    permissions:
      # We use github.token to download the build artifact from a previous runtime_build_and_test.yml run
      actions: read
    with:
      commit_sha: ${{ github.sha }}
      release_channel: stable
      dist_tag: canary,next
      enableFailureNotification: true
      dry: false
    secrets:
      DISCORD_WEBHOOK_URL: ${{ secrets.DISCORD_WEBHOOK_URL }}
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  publish_prerelease_experimental:
    name: Publish to Experimental channel
    uses: facebook/react/.github/workflows/runtime_prereleases.yml@main
    permissions:
      # We use github.token to download the build artifact from a previous runtime_build_and_test.yml run
      actions: read
    # NOTE: Intentionally running these jobs sequentially because npm
    # will sometimes fail if you try to concurrently publish two
    # different versions of the same package, even if they use different
    # dist tags.
    needs: publish_prerelease_canary
    with:
      commit_sha: ${{ github.sha }}
      release_channel: experimental
      dist_tag: experimental
      enableFailureNotification: true
      dry: false
    secrets:
      DISCORD_WEBHOOK_URL: ${{ secrets.DISCORD_WEBHOOK_URL }}
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
