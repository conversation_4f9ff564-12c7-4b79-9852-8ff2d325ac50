name: (Comp<PERSON>) Playground

on:
  push:
    branches: [main]
  pull_request:
    paths:
      - compiler/**
      - .github/workflows/compiler_playground.yml

permissions: {}

concurrency:
  group: ${{ github.workflow }}-${{ github.ref_name }}-${{ github.event.pull_request.number || github.run_id }}
  cancel-in-progress: true

env:
  TZ: /usr/share/zoneinfo/America/Los_Angeles
  # https://github.com/actions/cache/blob/main/tips-and-workarounds.md#cache-segment-restore-timeout
  SEGMENT_DOWNLOAD_TIMEOUT_MINS: 1

defaults:
  run:
    working-directory: compiler/apps/playground

jobs:
  playground:
    name: Test playground
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: yarn
          cache-dependency-path: compiler/**/yarn.lock
      - name: Restore cached node_modules
        uses: actions/cache@v4
        id: node_modules
        with:
          path: |
            **/node_modules
          key: compiler-and-playground-node_modules-v6-${{ runner.arch }}-${{ runner.os }}-${{ hashFiles('compiler/**/yarn.lock') }}
      - run: yarn install --frozen-lockfile
        if: steps.node_modules.outputs.cache-hit != 'true'
        working-directory: compiler
      - run: yarn install --frozen-lockfile
        if: steps.node_modules.outputs.cache-hit != 'true'
      - name: Check Playwright version
        id: playwright_version
        run: echo "playwright_version=$(npm ls @playwright/test | grep @playwright | sed 's/.*@//' | head -1)" >> "$GITHUB_OUTPUT"
      - name: Cache Playwright Browsers for version ${{ steps.playwright_version.outputs.playwright_version }}
        id: cache_playwright_browsers
        uses: actions/cache@v4
        with:
          path: ~/.cache/ms-playwright
          key: playwright-browsers-v6-${{ runner.arch }}-${{ runner.os }}-${{ steps.playwright_version.outputs.playwright_version }}
      - run: npx playwright install --with-deps chromium
        if: steps.cache_playwright_browsers.outputs.cache-hit != 'true'
      - run: npx playwright install-deps
        if: steps.cache_playwright_browsers.outputs.cache-hit == 'true'
      - run: CI=true yarn test
      - run: ls -R test-results
        if: '!cancelled()'
      - name: Archive test results
        if: '!cancelled()'
        uses: actions/upload-artifact@v4
        with:
          name: test-results
          path: compiler/apps/playground/test-results
          if-no-files-found: ignore
