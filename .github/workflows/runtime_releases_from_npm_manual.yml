name: (Runtime) Publish Releases from NPM Manual

on:
  workflow_dispatch:
    inputs:
      version_to_promote:
        required: true
        description: Current npm version (non-experimental) to promote
        type: string
      version_to_publish:
        required: true
        description: Version to publish for the specified packages
        type: string
      only_packages:
        description: Packages to publish (space separated)
        type: string
      skip_packages:
        description: Packages to NOT publish (space separated)
        type: string
      tags:
        description: NPM tags (space separated)
        type: string
        default: untagged
      dry:
        required: true
        description: Dry run instead of publish?
        type: boolean
        default: true
      force_notify:
        description: Force a Discord notification?
        type: boolean
        default: false

permissions: {}

env:
  TZ: /usr/share/zoneinfo/America/Los_Angeles
  # https://github.com/actions/cache/blob/main/tips-and-workarounds.md#cache-segment-restore-timeout
  SEGMENT_DOWNLOAD_TIMEOUT_MINS: 1
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

jobs:
  notify:
    if: ${{ inputs.force_notify || inputs.dry == false || inputs.dry == 'false' }}
    runs-on: ubuntu-latest
    steps:
      - name: Discord Webhook Action
        uses: tsickert/discord-webhook@86dc739f3f165f16dadc5666051c367efa1692f4
        with:
          webhook-url: ${{ secrets.DISCORD_WEBHOOK_URL }}
          embed-author-name: ${{ github.event.sender.login }}
          embed-author-url: ${{ github.event.sender.html_url }}
          embed-author-icon-url: ${{ github.event.sender.avatar_url }}
          embed-title: "⚠️ Publishing release from NPM${{ (inputs.dry && ' (dry run)') || '' }}"
          embed-description: |
            ```json
            ${{ toJson(inputs) }}
            ```
          embed-url: https://github.com/facebook/react/actions/runs/${{ github.run_id }}

  publish:
    name: Publish releases
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: yarn
          cache-dependency-path: yarn.lock
      - name: Restore cached node_modules
        uses: actions/cache@v4
        id: node_modules
        with:
          path: |
            **/node_modules
          key: runtime-release-node_modules-v6-${{ runner.arch }}-${{ runner.os }}-${{ hashFiles('yarn.lock', 'scripts/release/yarn.lock') }}
      - name: Ensure clean build directory
        run: rm -rf build
      - run: yarn install --frozen-lockfile
        if: steps.node_modules.outputs.cache-hit != 'true'
      - run: yarn --cwd scripts/release install --frozen-lockfile
        if: steps.node_modules.outputs.cache-hit != 'true'
      - run: cp ./scripts/release/ci-npmrc ~/.npmrc
      - if: '${{ inputs.only_packages }}'
        name: 'Prepare ${{ inputs.only_packages }} from NPM'
        run: |
          scripts/release/prepare-release-from-npm.js \
            --ci \
            --skipTests \
            --version=${{ inputs.version_to_promote }} \
            --publishVersion=${{ inputs.version_to_publish }} \
            --onlyPackages=${{ inputs.only_packages }}
      - if: '${{ inputs.skip_packages }}'
        name: 'Prepare all packages EXCEPT ${{ inputs.skip_packages }} from NPM'
        run: |
          scripts/release/prepare-release-from-npm.js \
            --ci \
            --skipTests \
            --version=${{ inputs.version_to_promote }} \
            --publishVersion=${{ inputs.version_to_publish }} \
            --skipPackages=${{ inputs.skip_packages }}
      - name: Check prepared files
        run: ls -R build/node_modules
      - if: '${{ inputs.only_packages }}'
        name: 'Publish ${{ inputs.only_packages }}'
        run: |
          scripts/release/publish.js \
            --ci \
            --tags=${{ inputs.tags }} \
            --publishVersion=${{ inputs.version_to_publish }} \
            --onlyPackages=${{ inputs.only_packages }} ${{ (inputs.dry && '') || '\'}}
            ${{ inputs.dry && '--dry' || '' }}
      - if: '${{ inputs.skip_packages }}'
        name: 'Publish all packages EXCEPT ${{ inputs.skip_packages }}'
        run: |
          scripts/release/publish.js \
            --ci \
            --tags=${{ inputs.tags }} \
            --publishVersion=${{ inputs.version_to_publish }} \
            --skipPackages=${{ inputs.skip_packages }} ${{ (inputs.dry && '') || '\'}}
            ${{ inputs.dry && '--dry' || '' }}
      - name: Archive released package for debugging
        uses: actions/upload-artifact@v4
        with:
          name: build
          path: |
            ./build/node_modules
