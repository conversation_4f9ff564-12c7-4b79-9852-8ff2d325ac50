# Configuration for stale action workflow - https://github.com/actions/stale
name: (Shared) Manage stale issues and PRs
on:
  schedule:
    # Run hourly
    - cron: '0 * * * *'
  workflow_dispatch:

permissions:
  # https://github.com/actions/stale/tree/v9/?tab=readme-ov-file#recommended-permissions
  issues: write
  pull-requests: write

env:
  TZ: /usr/share/zoneinfo/America/Los_Angeles

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          # --- Issues & PRs ---
          # Number of days of inactivity before an issue or PR becomes stale
          days-before-stale: 90
          # Number of days of inactivity before a stale issue or PR is closed
          days-before-close: 7
          # API calls per run
          operations-per-run: 100

          # --- Issues ---
          stale-issue-label: "Resolution: Stale"
          # Comment to post when marking an issue as stale
          stale-issue-message: >
            This issue has been automatically marked as stale.
            **If this issue is still affecting you, please leave any comment** (for example, "bump"), and we'll keep it open.
            We are sorry that we haven't been able to prioritize it yet. If you have any new additional information, please include it with your comment!
          # Comment to post when closing a stale issue
          close-issue-message: >
            Closing this issue after a prolonged period of inactivity. If this issue is still present in the latest release, please create a new issue with up-to-date information. Thank you!
          # Issues with these labels will never be considered stale
          exempt-issue-labels: "Partner,React Core Team,Resolution: Backlog,Type: Bug,Type: Discussion,Type: Needs Investigation,Type: Regression,Type: Feature Request,Type: Enhancement"

          # --- PRs ---
          stale-pr-label: "Resolution: Stale"
          # Comment to post when marking a pull request as stale
          stale-pr-message: >
            This pull request has been automatically marked as stale.
            **If this pull request is still relevant, please leave any comment** (for example, "bump"), and we'll keep it open.
            We are sorry that we haven't been able to prioritize reviewing it yet. Your contribution is very much appreciated.
          # Comment to post when closing a stale pull request
          close-pr-message: >
            Closing this pull request after a prolonged period of inactivity. If this issue is still present in the latest release, please ask for this pull request to be reopened. Thank you!
          # PRs with these labels will never be considered stale
          exempt-pr-labels: "Partner,React Core Team,Resolution: Backlog,Type: Bug,Type: Discussion,Type: Needs Investigation,Type: Regression,Type: Feature Request,Type: Enhancement"
