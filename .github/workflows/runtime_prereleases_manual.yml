name: (Runtime) Publish Prereleases Manual

on:
  workflow_dispatch:
    inputs:
      prerelease_commit_sha:
        required: true
      only_packages:
        description: Packages to publish (space separated)
        type: string
      skip_packages:
        description: Packages to NOT publish (space separated)
        type: string
      dry:
        required: true
        description: Dry run instead of publish?
        type: boolean
        default: true
      experimental_only:
        type: boolean
        description: Only publish to the experimental tag
        default: false
      force_notify:
        description: Force a Discord notification?
        type: boolean
        default: false

permissions: {}

env:
  TZ: /usr/share/zoneinfo/America/Los_Angeles

jobs:
  notify:
    if: ${{ inputs.force_notify || inputs.dry == false || inputs.dry == 'false' }}
    runs-on: ubuntu-latest
    steps:
      - name: Discord Webhook Action
        uses: tsickert/discord-webhook@86dc739f3f165f16dadc5666051c367efa1692f4
        with:
          webhook-url: ${{ secrets.DISCORD_WEBHOOK_URL }}
          embed-author-name: ${{ github.event.sender.login }}
          embed-author-url: ${{ github.event.sender.html_url }}
          embed-author-icon-url: ${{ github.event.sender.avatar_url }}
          embed-title: "⚠️ Publishing ${{ inputs.experimental_only && 'EXPERIMENTAL' || 'CANARY & EXPERIMENTAL' }} release ${{ (inputs.dry && ' (dry run)') || '' }}"
          embed-description: |
            ```json
            ${{ toJson(inputs) }}
            ```
          embed-url: https://github.com/facebook/react/actions/runs/${{ github.run_id }}

  publish_prerelease_canary:
    if: ${{ !inputs.experimental_only }}
    name: Publish to Canary channel
    uses: facebook/react/.github/workflows/runtime_prereleases.yml@main
    permissions:
      # We use github.token to download the build artifact from a previous runtime_build_and_test.yml run
      actions: read
    with:
      commit_sha: ${{ inputs.prerelease_commit_sha }}
      release_channel: stable
      # The tags to use when publishing canaries. The main one we should
      # always include is "canary" but we can use multiple (e.g. alpha,
      # beta, rc). To declare multiple, use a comma-separated string, like
      # this:
      #   dist_tag: "canary,alpha,beta,rc"
      #
      # TODO: We currently tag canaries with "next" in addition to "canary"
      # because this used to be called the "next" channel and some
      # downstream consumers might still expect that tag. We can remove this
      # after some time has elapsed and the change has been communicated.
      dist_tag: canary,next
      only_packages: ${{ inputs.only_packages }}
      skip_packages: ${{ inputs.skip_packages }}
      dry: ${{ inputs.dry }}
    secrets:
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  publish_prerelease_experimental:
    name: Publish to Experimental channel
    uses: facebook/react/.github/workflows/runtime_prereleases.yml@main
    permissions:
      # We use github.token to download the build artifact from a previous runtime_build_and_test.yml run
      actions: read
    # NOTE: Intentionally running these jobs sequentially because npm
    # will sometimes fail if you try to concurrently publish two
    # different versions of the same package, even if they use different
    # dist tags.
    needs: publish_prerelease_canary
    # Ensures the job runs even if canary is skipped
    if: always()
    with:
      commit_sha: ${{ inputs.prerelease_commit_sha }}
      release_channel: experimental
      dist_tag: experimental
      only_packages: ${{ inputs.only_packages }}
      skip_packages: ${{ inputs.skip_packages }}
      dry: ${{ inputs.dry }}
    secrets:
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
