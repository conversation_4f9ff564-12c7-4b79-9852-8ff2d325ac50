name: (Compiler) Publish Prereleases Nightly

on:
  schedule:
    # At 10 minutes past 16:00 on <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>
    <PERSON> <PERSON><PERSON>: 10 16 * * 1,2,3,4,5

permissions: {}

env:
  TZ: /usr/share/zoneinfo/America/Los_Angeles

jobs:
  publish_prerelease_experimental:
    name: Publish to Experimental channel
    uses: facebook/react/.github/workflows/compiler_prereleases.yml@main
    with:
      commit_sha: ${{ github.sha }}
      release_channel: experimental
      dist_tag: experimental
      version_name: '0.0.0'
    secrets:
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
