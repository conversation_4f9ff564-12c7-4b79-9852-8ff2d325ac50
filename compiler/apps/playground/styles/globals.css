/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  @font-face {
    font-family: "Source Code Pro";
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url("/fonts/Source-Code-Pro-Regular.woff2") format("woff2");
  }

  @font-face {
    font-family: "Optimistic Display";
    src: url("/fonts/Optimistic_Display_W_Lt.woff2") format("woff2");
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: "Optimistic Display";
    src: url("/fonts/Optimistic_Display_W_Md.woff2") format("woff2");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: "Optimistic Display";
    src: url("/fonts/Optimistic_Display_W_Bd.woff2") format("woff2");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }

  html,
  body {
    padding: 0;
    margin: 0;
  }

  a {
    color: inherit;
    text-decoration: none;
  }

  * {
    box-sizing: border-box;
  }
}

@layer utilities {
  /* Chrome, Safari, Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE, Edge */
    scrollbar-width: none; /* Firefox */
  }
}
