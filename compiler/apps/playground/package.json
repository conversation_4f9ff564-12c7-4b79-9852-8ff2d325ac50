{"name": "playground", "version": "0.1.0", "private": true, "scripts": {"dev": "cd ../.. && concurrently --kill-others -n compiler,runtime,playground \"yarn workspace babel-plugin-react-compiler run watch\" \"yarn workspace react-compiler-runtime run watch\" \"wait-on packages/babel-plugin-react-compiler/dist/index.js && cd apps/playground && NODE_ENV=development next dev\"", "build:compiler": "cd ../.. && concurrently -n compiler,runtime \"yarn workspace babel-plugin-react-compiler run build --dts\" \"yarn workspace react-compiler-runtime run build\"", "build": "yarn build:compiler && next build", "postbuild": "node ./scripts/downloadFonts.js", "preinstall": "cd ../.. && yarn install --frozen-lockfile", "postinstall": "./scripts/link-compiler.sh", "vercel-build": "yarn build", "start": "next start", "lint": "next lint", "test": "playwright test --workers=4"}, "dependencies": {"@babel/core": "^7.18.9", "@babel/generator": "^7.18.9", "@babel/parser": "^7.18.9", "@babel/plugin-syntax-typescript": "^7.18.9", "@babel/plugin-transform-block-scoping": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.9", "@babel/preset-react": "^7.18.9", "@babel/preset-typescript": "^7.26.0", "@babel/traverse": "^7.18.9", "@babel/types": "7.26.3", "@heroicons/react": "^1.0.6", "@monaco-editor/react": "^4.4.6", "@playwright/test": "^1.51.1", "@use-gesture/react": "^10.2.22", "hermes-eslint": "^0.25.0", "hermes-parser": "^0.25.0", "invariant": "^2.2.4", "lz-string": "^1.5.0", "monaco-editor": "^0.52.0", "next": "^15.2.0-canary.64", "notistack": "^3.0.0-alpha.7", "prettier": "^3.3.3", "pretty-format": "^29.3.1", "re-resizable": "^6.9.16", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@types/node": "18.11.9", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "autoprefixer": "^10.4.13", "clsx": "^1.2.1", "concurrently": "^7.4.0", "eslint": "^8.28.0", "eslint-config-next": "^15.0.1", "monaco-editor-webpack-plugin": "^7.1.0", "postcss": "^8.4.31", "tailwindcss": "^3.2.4", "wait-on": "^7.2.0"}}