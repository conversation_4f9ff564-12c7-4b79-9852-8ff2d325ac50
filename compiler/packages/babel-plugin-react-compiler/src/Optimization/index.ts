/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

export {constantPropagation} from './ConstantPropagation';
export {deadCodeElimination} from './DeadCodeElimination';
export {pruneMaybeThrows} from './PruneMaybeThrows';
export {inlineJsxTransform} from './InlineJsxTransform';
