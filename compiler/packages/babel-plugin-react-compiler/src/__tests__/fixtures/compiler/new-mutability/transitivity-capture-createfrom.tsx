import {useMemo} from 'react';
import {
  typedCapture,
  typed<PERSON>reateFrom,
  typedMutate,
  ValidateMemoization,
} from 'shared-runtime';

function Component({a, b}: {a: number; b: number}) {
  const x = useMemo(() => ({a}), [a, b]);
  const y = typedCapture(x);
  const z = typedCreateFrom(y);
  // mutates x
  typedMutate(z, b);

  return <ValidateMemoization inputs={[a, b]} output={x} />;
}

export const FIXTURE_ENTRYPOINT = {
  fn: Component,
  params: [{a: 0, b: 0}],
  sequentialRenders: [
    {a: 0, b: 0},
    {a: 0, b: 1},
    {a: 1, b: 1},
    {a: 0, b: 0},
  ],
};
